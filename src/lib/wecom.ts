import { WECOM_AGENT_ID, WECOM_CORP_ID } from "astro:env/client";
import { WECOM_CORP_SECRET } from "astro:env/server";


const WECOM_BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin";

interface WecomClientOptions {
  corpId: string;
  corpSecret: string;
  agentId: string;
}

const wecom = (options: WecomClientOptions) => {
  return {
    getAuthorizeUrl: ({ redirectUrl }: { redirectUrl: string }) => {
      return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${options.corpId}&redirect_uri=${encodeURIComponent(
        redirectUrl
      )}&response_type=code&scope=snsapi_privateinfo&agentid=${options.agentId}`;
    }
  }
}

export default wecom({
  corpId: WECOM_CORP_ID,
  corpSecret: WECOM_CORP_SECRET,
  agentId: WECOM_AGENT_ID,
});

